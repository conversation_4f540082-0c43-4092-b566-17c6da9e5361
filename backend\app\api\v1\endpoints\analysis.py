"""
Analysis endpoints
"""

from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.schemas.analysis import DreamAnalysisRequest, DreamAnalysisResponse, AnalysisResultResponse
from app.services.analysis_service import AnalysisService
from app.services.dream_service import DreamService

router = APIRouter()


@router.post("/{dream_id}", response_model=DreamAnalysisResponse, status_code=status.HTTP_201_CREATED)
async def create_analysis(
    dream_id: str,
    analysis_request: DreamAnalysisRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new dream analysis"""

    # Check if dream exists and belongs to user
    dream_service = DreamService(db)
    dream = await dream_service.get_dream_by_id(dream_id, current_user.user_id)

    if not dream:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dream not found"
        )

    # Create analysis
    analysis_service = AnalysisService(db)
    analysis = await analysis_service.create_analysis(dream_id, analysis_request.analysis_type)

    return DreamAnalysisResponse(
        analysis_id=analysis.analysis_id,
        dream_id=dream_id,
        status=analysis.status,
        estimated_time=300 if analysis_request.analysis_type == "comprehensive" else 60
    )


@router.get("/{analysis_id}", response_model=AnalysisResultResponse)
async def get_analysis(
    analysis_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get analysis by ID"""

    analysis_service = AnalysisService(db)
    analysis = await analysis_service.get_analysis_by_id(analysis_id)

    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )

    # Check if analysis belongs to user's dream
    dream_service = DreamService(db)
    dream = await dream_service.get_dream_by_id(analysis.dream_id, current_user.user_id)

    if not dream:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )

    return analysis


@router.get("/dream/{dream_id}", response_model=AnalysisResultResponse)
async def get_dream_analysis(
    dream_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get analysis for a specific dream"""

    # Check if dream exists and belongs to user
    dream_service = DreamService(db)
    dream = await dream_service.get_dream_by_id(dream_id, current_user.user_id)

    if not dream:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dream not found"
        )

    # Get analysis
    analysis_service = AnalysisService(db)
    analysis = await analysis_service.get_dream_analysis(dream_id)

    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analysis not found"
        )

    return {
        "analysis_id": analysis.analysis_id,
        "dream_id": analysis.dream_id,
        "status": analysis.status.value,
        "analysis_type": analysis.analysis_type.value,
        "keywords": analysis.keywords or [],
        "psychological_hints": analysis.psychological_hints,
        "symbolic_meaning": analysis.symbolic_meaning,
        "suggestions": analysis.suggestions or [],
        "poetry_quote": analysis.poetry_quote,
        "confidence_score": analysis.confidence_score,
        "quality_rating": analysis.quality_rating,
        "created_at": analysis.created_at,
        "started_at": analysis.started_at,
        "completed_at": analysis.completed_at,
        "error_message": analysis.error_message
    }


@router.get("/statistics/overview", response_model=Dict[str, Any])
async def get_analysis_statistics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get analysis statistics for user"""

    analysis_service = AnalysisService(db)
    stats = await analysis_service.get_user_analysis_statistics(current_user.user_id)

    return stats


@router.get("/dream/{dream_id}/keywords")
async def get_dream_keywords(
    dream_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get keywords for a dream"""

    # Check if dream exists and belongs to user
    dream_service = DreamService(db)
    dream = await dream_service.get_dream_by_id(dream_id, current_user.user_id)

    if not dream:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dream not found"
        )

    # Get keywords
    analysis_service = AnalysisService(db)
    keywords = await analysis_service.get_dream_keywords(dream_id)

    return {
        "dream_id": dream_id,
        "keywords": [
            {
                "keyword_id": kw.keyword_id,
                "keyword": kw.keyword,
                "weight": kw.weight,
                "category": kw.category
            }
            for kw in keywords
        ]
    }


@router.post("/mock/{dream_id}", response_model=Dict[str, Any])
async def mock_analysis(
    dream_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Mock analysis for development purposes"""

    # Check if dream exists and belongs to user
    dream_service = DreamService(db)
    dream = await dream_service.get_dream_by_id(dream_id, current_user.user_id)

    if not dream:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dream not found"
        )

    # Generate mock analysis
    analysis_service = AnalysisService(db)
    mock_results = await analysis_service.mock_analysis(dream_id)

    return {
        "dream_id": dream_id,
        "analysis": mock_results,
        "note": "This is a mock analysis for development purposes"
    }
