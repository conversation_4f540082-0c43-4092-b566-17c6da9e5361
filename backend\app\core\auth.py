"""
Authentication and authorization utilities
"""

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import Optional
import structlog

from app.core.database import get_db
from app.core.security import security, jwt_manager
from app.models.user import User
from app.core.config import settings

logger = structlog.get_logger()

# Security scheme
security_scheme = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security_scheme),
    db: AsyncSession = Depends(get_db)
) -> User:
    """
    Get current authenticated user from JW<PERSON> token
    """

    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # Decode JWT token
        payload = jwt_manager.verify_token(credentials.credentials, "access")
        user_id: str = payload.get("sub")

        if user_id is None:
            raise credentials_exception

    except Exception as e:
        logger.error("Token validation failed", error=str(e))
        raise credentials_exception

    # Get user from database
    result = await db.execute(
        select(User).where(
            User.user_id == user_id,
            User.is_active == True,
            User.is_deleted == False
        )
    )
    user = result.scalar_one_or_none()

    if user is None:
        raise credentials_exception

    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current active user (additional check for active status)
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


async def get_current_verified_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current verified user (requires email verification)
    """
    if not current_user.is_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email not verified"
        )
    return current_user


def get_optional_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security_scheme),
    db: AsyncSession = Depends(get_db)
) -> Optional[User]:
    """
    Get current user if token is provided, otherwise return None
    Used for endpoints that work with or without authentication
    """

    if not credentials:
        return None

    try:
        # This is a simplified version - in production you'd want proper error handling
        payload = jwt_manager.verify_token(credentials.credentials, "access")
        user_id: str = payload.get("sub")

        if user_id is None:
            return None

        # Get user from database (simplified)
        # In production, you'd want to make this async properly
        return None  # Placeholder

    except Exception:
        return None


class RoleChecker:
    """
    Role-based access control checker
    """

    def __init__(self, allowed_roles: list):
        self.allowed_roles = allowed_roles

    def __call__(self, current_user: User = Depends(get_current_user)):
        # For now, we don't have roles in the User model
        # This is a placeholder for future role-based access control

        # You could add a role field to the User model and check it here
        # if current_user.role not in self.allowed_roles:
        #     raise HTTPException(
        #         status_code=status.HTTP_403_FORBIDDEN,
        #         detail="Operation not permitted"
        #     )

        return current_user


# Convenience role checkers
require_admin = RoleChecker(["admin"])
require_moderator = RoleChecker(["admin", "moderator"])


def create_access_token(user_id: str, expires_delta: Optional[int] = None) -> str:
    """
    Create access token for user
    """
    return jwt_manager.create_access_token(
        data={"sub": user_id},
        expires_delta=expires_delta
    )


def create_refresh_token(user_id: str) -> str:
    """
    Create refresh token for user
    """
    return jwt_manager.create_refresh_token(data={"sub": user_id})


async def verify_refresh_token(token: str, db: AsyncSession) -> Optional[User]:
    """
    Verify refresh token and return user
    """
    try:
        payload = jwt_manager.verify_token(token, "refresh")
        user_id: str = payload.get("sub")

        if user_id is None:
            return None

        # Get user from database
        result = await db.execute(
            select(User).where(
                User.user_id == user_id,
                User.is_active == True,
                User.is_deleted == False
            )
        )
        user = result.scalar_one_or_none()

        return user

    except Exception as e:
        logger.error("Refresh token validation failed", error=str(e))
        return None


def hash_password(password: str) -> str:
    """
    Hash password
    """
    return security.hash_password(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify password
    """
    return security.verify_password(plain_password, hashed_password)
