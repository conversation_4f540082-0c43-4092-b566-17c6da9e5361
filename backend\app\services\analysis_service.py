"""
Analysis service
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from typing import Optional, Dict, Any, List
from datetime import datetime, timezone
import structlog

from app.models.dream import Dream, DreamKeyword
from app.models.analysis import DreamAnalysis, AnalysisStatus, AnalysisType
from app.core.exceptions import NotFoundException

logger = structlog.get_logger()


class AnalysisService:
    """Analysis service class"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_analysis(
        self,
        dream_id: str,
        analysis_type: str = "comprehensive"
    ) -> DreamAnalysis:
        """Create a new dream analysis"""

        # Check if dream exists
        dream = await self.db.get(Dream, dream_id)
        if not dream:
            raise NotFoundException("Dream not found")

        # Convert string to enum
        analysis_type_enum = AnalysisType.COMPREHENSIVE if analysis_type == "comprehensive" else AnalysisType.QUICK

        # Create analysis
        analysis = DreamAnalysis(
            dream_id=dream_id,
            user_id=dream.user_id,
            analysis_type=analysis_type_enum,
            status=AnalysisStatus.PENDING,
            started_at=datetime.now(timezone.utc)
        )

        self.db.add(analysis)
        await self.db.commit()
        await self.db.refresh(analysis)

        logger.info("Analysis created", analysis_id=analysis.analysis_id, dream_id=dream_id)
        return analysis

    async def get_analysis_by_id(self, analysis_id: str) -> Optional[DreamAnalysis]:
        """Get analysis by ID"""
        return await self.db.get(DreamAnalysis, analysis_id)

    async def get_dream_analysis(self, dream_id: str) -> Optional[DreamAnalysis]:
        """Get analysis for a dream"""
        result = await self.db.execute(
            select(DreamAnalysis).where(DreamAnalysis.dream_id == dream_id)
        )
        return result.scalar_one_or_none()

    async def update_analysis_status(self, analysis_id: str, status: AnalysisStatus) -> bool:
        """Update analysis status"""

        analysis = await self.get_analysis_by_id(analysis_id)
        if not analysis:
            return False

        analysis.status = status
        if status == AnalysisStatus.PROCESSING and not analysis.started_at:
            analysis.started_at = datetime.now(timezone.utc)

        await self.db.commit()
        return True

    async def complete_analysis(
        self,
        analysis_id: str,
        keywords: List[str] = None,
        psychological_hints: str = None,
        symbolic_meaning: str = None,
        suggestions: List[str] = None,
        poetry_quote: Dict[str, str] = None,
        confidence_score: float = None,
        quality_rating: int = None,
        processing_time: float = None,
        model_version: str = None
    ) -> bool:
        """Complete analysis with results"""

        analysis = await self.get_analysis_by_id(analysis_id)
        if not analysis:
            return False

        # Update analysis with new model fields
        analysis.status = AnalysisStatus.COMPLETED
        analysis.keywords = keywords or []
        analysis.psychological_hints = psychological_hints
        analysis.symbolic_meaning = symbolic_meaning
        analysis.suggestions = suggestions or []
        analysis.poetry_quote = poetry_quote
        analysis.confidence_score = confidence_score
        analysis.quality_rating = quality_rating
        analysis.processing_time = processing_time
        analysis.model_version = model_version
        analysis.completed_at = datetime.now(timezone.utc)

        # Update dream status
        dream = await self.db.get(Dream, analysis.dream_id)
        if dream:
            dream.status = "analyzed"
            if quality_rating:
                dream.quality_score = quality_rating * 20  # Convert 1-5 to 0-100

        await self.db.commit()
        logger.info("Analysis completed", analysis_id=analysis_id)
        return True

    async def fail_analysis(self, analysis_id: str, error_message: str) -> bool:
        """Mark analysis as failed"""

        analysis = await self.get_analysis_by_id(analysis_id)
        if not analysis:
            return False

        analysis.status = AnalysisStatus.FAILED
        analysis.error_message = error_message

        # Update dream status
        dream = await self.db.get(Dream, analysis.dream_id)
        if dream:
            dream.status = "analysis_failed"

        await self.db.commit()
        logger.error("Analysis failed", analysis_id=analysis_id, error=error_message)
        return True

    async def get_user_analysis_statistics(self, user_id: str) -> Dict[str, Any]:
        """Get analysis statistics for user"""

        # Get user's dreams
        dreams_result = await self.db.execute(
            select(Dream.dream_id).where(
                Dream.user_id == user_id,
                Dream.is_deleted == False
            )
        )
        dream_ids = [row[0] for row in dreams_result.fetchall()]

        if not dream_ids:
            return {
                "total_analyses": 0,
                "completed_analyses": 0,
                "pending_analyses": 0,
                "failed_analyses": 0
            }

        # Total analyses
        total_result = await self.db.execute(
            select(func.count()).where(DreamAnalysis.dream_id.in_(dream_ids))
        )
        total_analyses = total_result.scalar()

        # Completed analyses
        completed_result = await self.db.execute(
            select(func.count()).where(
                DreamAnalysis.dream_id.in_(dream_ids),
                DreamAnalysis.status == AnalysisStatus.COMPLETED
            )
        )
        completed_analyses = completed_result.scalar()

        # Pending analyses
        pending_result = await self.db.execute(
            select(func.count()).where(
                DreamAnalysis.dream_id.in_(dream_ids),
                DreamAnalysis.status.in_([AnalysisStatus.PENDING, AnalysisStatus.PROCESSING])
            )
        )
        pending_analyses = pending_result.scalar()

        # Failed analyses
        failed_result = await self.db.execute(
            select(func.count()).where(
                DreamAnalysis.dream_id.in_(dream_ids),
                DreamAnalysis.status == AnalysisStatus.FAILED
            )
        )
        failed_analyses = failed_result.scalar()

        return {
            "total_analyses": total_analyses,
            "completed_analyses": completed_analyses,
            "pending_analyses": pending_analyses,
            "failed_analyses": failed_analyses
        }

    async def get_dream_keywords(self, dream_id: str) -> List[DreamKeyword]:
        """Get keywords for a dream"""
        result = await self.db.execute(
            select(DreamKeyword).where(DreamKeyword.dream_id == dream_id)
        )
        return list(result.scalars().all())

    async def mock_analysis(self, dream_id: str) -> Dict[str, Any]:
        """Mock analysis for development (placeholder)"""
        # Note: dream_id is used for potential future customization
        _ = dream_id  # Acknowledge parameter usage

        return {
            "keywords": ["adventure", "anxiety", "family", "water", "flying", "house"],
            "psychological_hints": "This dream suggests a desire for freedom while maintaining connection to home and family.",
            "symbolic_meaning": "Water represents emotions, flying symbolizes freedom, and house represents security and family bonds.",
            "suggestions": [
                "Consider journaling about your relationship with family",
                "Explore themes of independence in your waking life",
                "Reflect on your emotional relationship with change"
            ],
            "poetry_quote": {
                "content": "家是心灵的港湾，梦是自由的翅膀",
                "author": "古诗词",
                "description": "关于家庭与自由的平衡"
            },
            "confidence_score": 0.85,
            "quality_rating": 4
        }
