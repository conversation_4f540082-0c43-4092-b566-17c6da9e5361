"""
Analysis service
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from typing import Optional, Dict, Any, List
from datetime import datetime
import structlog

from app.models.dream import Dream, DreamAnalysis, DreamKeyword
from app.core.exceptions import NotFoundException

logger = structlog.get_logger()


class AnalysisService:
    """Analysis service class"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_analysis(
        self,
        dream_id: str,
        analysis_type: str = "comprehensive"
    ) -> DreamAnalysis:
        """Create a new dream analysis"""
        
        # Check if dream exists
        dream = await self.db.get(Dream, dream_id)
        if not dream:
            raise NotFoundException("Dream not found")
        
        # Create analysis
        analysis = DreamAnalysis(
            dream_id=dream_id,
            analysis_type=analysis_type,
            status="pending",
            progress=0
        )
        
        self.db.add(analysis)
        await self.db.commit()
        await self.db.refresh(analysis)
        
        logger.info("Analysis created", analysis_id=analysis.analysis_id, dream_id=dream_id)
        return analysis
    
    async def get_analysis_by_id(self, analysis_id: str) -> Optional[DreamAnalysis]:
        """Get analysis by ID"""
        return await self.db.get(DreamAnalysis, analysis_id)
    
    async def get_dream_analysis(self, dream_id: str) -> Optional[DreamAnalysis]:
        """Get analysis for a dream"""
        result = await self.db.execute(
            select(DreamAnalysis).where(DreamAnalysis.dream_id == dream_id)
        )
        return result.scalar_one_or_none()
    
    async def update_analysis_progress(self, analysis_id: str, progress: int, status: str = None) -> bool:
        """Update analysis progress"""
        
        analysis = await self.get_analysis_by_id(analysis_id)
        if not analysis:
            return False
        
        analysis.progress = progress
        if status:
            analysis.status = status
        
        analysis.updated_at = datetime.utcnow()
        
        await self.db.commit()
        return True
    
    async def complete_analysis(
        self,
        analysis_id: str,
        results: Dict[str, Any],
        keywords: List[Dict[str, Any]] = None
    ) -> bool:
        """Complete analysis with results"""
        
        analysis = await self.get_analysis_by_id(analysis_id)
        if not analysis:
            return False
        
        # Update analysis
        analysis.status = "completed"
        analysis.progress = 100
        analysis.results = results
        analysis.completed_at = datetime.utcnow()
        analysis.updated_at = datetime.utcnow()
        
        # Update dream status
        dream = await self.db.get(Dream, analysis.dream_id)
        if dream:
            dream.status = "analyzed"
            if "quality_score" in results:
                dream.quality_score = results["quality_score"]
        
        # Add keywords if provided
        if keywords:
            for keyword_data in keywords:
                keyword = DreamKeyword(
                    dream_id=analysis.dream_id,
                    keyword=keyword_data["keyword"],
                    weight=keyword_data.get("weight", 1.0),
                    category=keyword_data.get("category")
                )
                self.db.add(keyword)
        
        await self.db.commit()
        logger.info("Analysis completed", analysis_id=analysis_id)
        return True
    
    async def fail_analysis(self, analysis_id: str, error_message: str) -> bool:
        """Mark analysis as failed"""
        
        analysis = await self.get_analysis_by_id(analysis_id)
        if not analysis:
            return False
        
        analysis.status = "failed"
        analysis.error_message = error_message
        analysis.updated_at = datetime.utcnow()
        
        # Update dream status
        dream = await self.db.get(Dream, analysis.dream_id)
        if dream:
            dream.status = "error"
        
        await self.db.commit()
        logger.error("Analysis failed", analysis_id=analysis_id, error=error_message)
        return True
    
    async def get_user_analysis_statistics(self, user_id: str) -> Dict[str, Any]:
        """Get analysis statistics for user"""
        
        # Get user's dreams
        dreams_result = await self.db.execute(
            select(Dream.dream_id).where(
                Dream.user_id == user_id,
                Dream.is_deleted == False
            )
        )
        dream_ids = [row[0] for row in dreams_result.fetchall()]
        
        if not dream_ids:
            return {
                "total_analyses": 0,
                "completed_analyses": 0,
                "pending_analyses": 0,
                "failed_analyses": 0
            }
        
        # Total analyses
        total_result = await self.db.execute(
            select(func.count()).where(DreamAnalysis.dream_id.in_(dream_ids))
        )
        total_analyses = total_result.scalar()
        
        # Completed analyses
        completed_result = await self.db.execute(
            select(func.count()).where(
                DreamAnalysis.dream_id.in_(dream_ids),
                DreamAnalysis.status == "completed"
            )
        )
        completed_analyses = completed_result.scalar()
        
        # Pending analyses
        pending_result = await self.db.execute(
            select(func.count()).where(
                DreamAnalysis.dream_id.in_(dream_ids),
                DreamAnalysis.status.in_(["pending", "processing"])
            )
        )
        pending_analyses = pending_result.scalar()
        
        # Failed analyses
        failed_result = await self.db.execute(
            select(func.count()).where(
                DreamAnalysis.dream_id.in_(dream_ids),
                DreamAnalysis.status == "failed"
            )
        )
        failed_analyses = failed_result.scalar()
        
        return {
            "total_analyses": total_analyses,
            "completed_analyses": completed_analyses,
            "pending_analyses": pending_analyses,
            "failed_analyses": failed_analyses
        }
    
    async def get_dream_keywords(self, dream_id: str) -> List[DreamKeyword]:
        """Get keywords for a dream"""
        result = await self.db.execute(
            select(DreamKeyword).where(DreamKeyword.dream_id == dream_id)
        )
        return list(result.scalars().all())
    
    async def mock_analysis(self, dream_id: str) -> Dict[str, Any]:
        """Mock analysis for development (placeholder)"""
        
        return {
            "quality_score": 85,
            "themes": ["adventure", "anxiety", "family"],
            "sentiment": "mixed",
            "symbols": ["water", "flying", "house"],
            "interpretation": "This dream suggests a desire for freedom while maintaining connection to home and family.",
            "recommendations": [
                "Consider journaling about your relationship with family",
                "Explore themes of independence in your waking life"
            ]
        }
