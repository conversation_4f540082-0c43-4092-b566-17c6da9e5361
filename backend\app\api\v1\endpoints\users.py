"""
Users endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.schemas.user import (
    UserResponse,
    UserUpdate,
    PasswordChangeRequest,
    UserStatistics,
    UserDeleteRequest
)
from app.services.user_service import UserService
from app.core.exceptions import ValidationException

router = APIRouter()


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """Get current user information"""
    return current_user


@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_data: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update current user information"""
    
    user_service = UserService(db)
    
    try:
        updated_user = await user_service.update_user(current_user.user_id, user_data)
        if not updated_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        return updated_user
    
    except ValidationException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/me/change-password", status_code=status.HTTP_200_OK)
async def change_password(
    password_data: PasswordChangeRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Change user password"""
    
    user_service = UserService(db)
    
    try:
        success = await user_service.change_password(
            current_user.user_id,
            password_data.current_password,
            password_data.new_password
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return {"message": "Password changed successfully"}
    
    except ValidationException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/me/statistics", response_model=UserStatistics)
async def get_user_statistics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user statistics"""
    
    user_service = UserService(db)
    stats = await user_service.get_user_statistics(current_user.user_id)
    
    return UserStatistics(**stats)


@router.put("/me/profile", response_model=Dict[str, Any])
async def update_user_profile(
    profile_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update user profile"""
    
    user_service = UserService(db)
    success = await user_service.update_user_profile(current_user.user_id, profile_data)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User profile not found"
        )
    
    return {"message": "Profile updated successfully"}


@router.post("/me/verify-email", status_code=status.HTTP_200_OK)
async def verify_email(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Verify user email (placeholder)"""
    
    user_service = UserService(db)
    success = await user_service.verify_email(current_user.user_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return {"message": "Email verified successfully"}


@router.delete("/me", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user_account(
    delete_request: UserDeleteRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete user account"""
    
    # Verify password
    from app.core.security import security
    if not security.verify_password(delete_request.password, current_user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid password"
        )
    
    # Verify confirmation
    if delete_request.confirmation != "DELETE":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid confirmation. Please type 'DELETE' to confirm."
        )
    
    user_service = UserService(db)
    success = await user_service.delete_user(current_user.user_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )


@router.get("/me/export", response_model=Dict[str, Any])
async def export_user_data(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Export user data (placeholder)"""
    
    # This is a placeholder implementation
    # In a real application, you would generate and return user data export
    
    return {
        "message": "Data export initiated",
        "user_id": current_user.user_id,
        "export_id": "export_123456",
        "estimated_time": "5-10 minutes",
        "note": "You will receive an email when the export is ready"
    }


@router.get("/me/activity", response_model=Dict[str, Any])
async def get_user_activity(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user activity summary"""
    
    # This is a placeholder implementation
    # In a real application, you would query user activity data
    
    return {
        "user_id": current_user.user_id,
        "last_login": current_user.last_login_at,
        "total_sessions": 0,
        "recent_activity": [
            {
                "action": "dream_created",
                "timestamp": "2024-01-01T10:00:00Z",
                "details": "Created new dream entry"
            },
            {
                "action": "analysis_requested",
                "timestamp": "2024-01-01T10:05:00Z",
                "details": "Requested dream analysis"
            }
        ]
    }
