"""
Analysis schemas
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class AnalysisTypeEnum(str, Enum):
    """Analysis type enumeration"""
    QUICK = "quick"
    COMPREHENSIVE = "comprehensive"


class AnalysisStatusEnum(str, Enum):
    """Analysis status enumeration"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class DreamAnalysisRequest(BaseModel):
    """Dream analysis request schema"""
    analysis_type: AnalysisTypeEnum = Field(AnalysisTypeEnum.COMPREHENSIVE, description="Type of analysis to perform")


class DreamAnalysisResponse(BaseModel):
    """Dream analysis response schema"""
    analysis_id: str
    dream_id: str
    status: AnalysisStatusEnum
    estimated_time: Optional[int] = Field(None, description="Estimated completion time in seconds")


class AnalysisResultBase(BaseModel):
    """Base analysis result schema"""
    analysis_id: str
    dream_id: str
    user_id: str
    analysis_type: AnalysisTypeEnum
    status: AnalysisStatusEnum


class AnalysisResultResponse(AnalysisResultBase):
    """Complete analysis result response schema"""
    keywords: List[str] = []
    psychological_hints: Optional[str] = None
    symbolic_meaning: Optional[str] = None
    suggestions: List[str] = []
    poetry_quote: Optional[Dict[str, str]] = None
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Analysis confidence score (0-1)")
    quality_rating: Optional[int] = Field(None, ge=1, le=5, description="Quality rating (1-5 stars)")
    processing_time: Optional[float] = Field(None, description="Processing time in seconds")
    model_version: Optional[str] = None
    error_message: Optional[str] = None
    
    # Timestamps
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    updated_at: datetime

    class Config:
        from_attributes = True


class PoetryQuoteBase(BaseModel):
    """Base poetry quote schema"""
    content: str = Field(..., min_length=1, max_length=1000)
    description: Optional[str] = Field(None, max_length=500)
    author: Optional[str] = Field(None, max_length=100)
    source: Optional[str] = Field(None, max_length=200)
    category: str = Field(..., min_length=1, max_length=50)
    keywords: List[str] = []
    emotion_tags: List[str] = []


class PoetryQuoteCreate(PoetryQuoteBase):
    """Poetry quote creation schema"""
    is_active: bool = True


class PoetryQuoteUpdate(BaseModel):
    """Poetry quote update schema"""
    content: Optional[str] = Field(None, min_length=1, max_length=1000)
    description: Optional[str] = Field(None, max_length=500)
    author: Optional[str] = Field(None, max_length=100)
    source: Optional[str] = Field(None, max_length=200)
    category: Optional[str] = Field(None, min_length=1, max_length=50)
    keywords: Optional[List[str]] = None
    emotion_tags: Optional[List[str]] = None
    is_active: Optional[bool] = None


class PoetryQuoteResponse(PoetryQuoteBase):
    """Poetry quote response schema"""
    quote_id: str
    is_active: bool
    usage_count: int
    rating: Optional[float] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class AnalysisTaskBase(BaseModel):
    """Base analysis task schema"""
    dream_id: str
    user_id: str
    analysis_type: AnalysisTypeEnum
    priority: int = Field(0, description="Task priority (higher number = higher priority)")


class AnalysisTaskCreate(AnalysisTaskBase):
    """Analysis task creation schema"""
    max_retries: int = Field(3, ge=0, le=10)


class AnalysisTaskResponse(AnalysisTaskBase):
    """Analysis task response schema"""
    task_id: str
    status: AnalysisStatusEnum
    worker_id: Optional[str] = None
    retry_count: int
    max_retries: int
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    
    # Timestamps
    created_at: datetime
    scheduled_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    updated_at: datetime

    class Config:
        from_attributes = True


class AnalysisStatistics(BaseModel):
    """Analysis statistics schema"""
    total_analyses: int
    completed_analyses: int
    pending_analyses: int
    failed_analyses: int
    avg_processing_time: Optional[float] = None
    avg_confidence_score: Optional[float] = None
    avg_quality_rating: Optional[float] = None


class MockAnalysisRequest(BaseModel):
    """Mock analysis request schema for development"""
    dream_id: str
    include_poetry: bool = True
    quality_rating: Optional[int] = Field(None, ge=1, le=5)


class MockAnalysisResponse(BaseModel):
    """Mock analysis response schema"""
    dream_id: str
    analysis: Dict[str, Any]
    note: str = "This is a mock analysis for development purposes"
