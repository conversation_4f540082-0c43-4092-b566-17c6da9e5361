"""
Statistics endpoints
"""

from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, Optional
from datetime import date, datetime, timedelta

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.services.dream_service import DreamService
from app.services.analysis_service import AnalysisService
from app.services.user_service import UserService

router = APIRouter()


@router.get("/overview", response_model=Dict[str, Any])
async def get_overview_statistics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get overview statistics for user"""
    
    # Get dream statistics
    dream_service = DreamService(db)
    dream_stats = await dream_service.get_dream_statistics(current_user.user_id)
    
    # Get analysis statistics
    analysis_service = AnalysisService(db)
    analysis_stats = await analysis_service.get_user_analysis_statistics(current_user.user_id)
    
    # Get user statistics
    user_service = UserService(db)
    user_stats = await user_service.get_user_statistics(current_user.user_id)
    
    return {
        "user": {
            "user_id": current_user.user_id,
            "username": current_user.username,
            "email": current_user.email,
            "member_since": current_user.created_at,
            "account_age_days": (datetime.utcnow() - current_user.created_at).days
        },
        "dreams": dream_stats,
        "analysis": analysis_stats,
        "activity": user_stats
    }


@router.get("/dreams/monthly", response_model=Dict[str, Any])
async def get_monthly_dream_statistics(
    months: int = Query(12, ge=1, le=24),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get monthly dream statistics"""
    
    # This is a placeholder implementation
    # In a real application, you would query the database for monthly data
    
    end_date = datetime.utcnow().date()
    start_date = end_date - timedelta(days=months * 30)
    
    monthly_data = []
    for i in range(months):
        month_start = end_date - timedelta(days=(months - i) * 30)
        month_end = end_date - timedelta(days=(months - i - 1) * 30)
        
        monthly_data.append({
            "month": month_start.strftime("%Y-%m"),
            "dreams_count": 0,  # Would be calculated from database
            "analyzed_count": 0,
            "avg_quality_score": None
        })
    
    return {
        "period": f"{start_date} to {end_date}",
        "monthly_data": monthly_data
    }


@router.get("/emotions/distribution", response_model=Dict[str, Any])
async def get_emotion_distribution(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get emotion distribution statistics"""
    
    # This is a placeholder implementation
    # In a real application, you would analyze emotions from dreams
    
    return {
        "period": {
            "start_date": start_date,
            "end_date": end_date
        },
        "emotion_distribution": {
            "happy": 25,
            "anxious": 15,
            "excited": 20,
            "sad": 10,
            "confused": 12,
            "peaceful": 18
        },
        "top_emotions": [
            {"emotion": "happy", "count": 25, "percentage": 25.0},
            {"emotion": "excited", "count": 20, "percentage": 20.0},
            {"emotion": "peaceful", "count": 18, "percentage": 18.0}
        ]
    }


@router.get("/keywords/trending", response_model=Dict[str, Any])
async def get_trending_keywords(
    limit: int = Query(20, ge=1, le=50),
    days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get trending keywords from user's dreams"""
    
    # This is a placeholder implementation
    # In a real application, you would analyze keywords from dreams
    
    return {
        "period_days": days,
        "trending_keywords": [
            {"keyword": "flying", "count": 8, "weight": 0.95},
            {"keyword": "water", "count": 6, "weight": 0.87},
            {"keyword": "family", "count": 5, "weight": 0.82},
            {"keyword": "house", "count": 4, "weight": 0.76},
            {"keyword": "car", "count": 3, "weight": 0.71}
        ]
    }


@router.get("/quality/trends", response_model=Dict[str, Any])
async def get_quality_trends(
    days: int = Query(30, ge=7, le=365),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get dream quality trends"""
    
    # This is a placeholder implementation
    # In a real application, you would analyze quality scores over time
    
    return {
        "period_days": days,
        "average_quality": 78.5,
        "quality_trend": "improving",  # improving, declining, stable
        "daily_averages": [
            {"date": "2024-01-01", "avg_quality": 75},
            {"date": "2024-01-02", "avg_quality": 78},
            {"date": "2024-01-03", "avg_quality": 82}
        ]
    }


@router.get("/patterns/sleep", response_model=Dict[str, Any])
async def get_sleep_patterns(
    days: int = Query(30, ge=7, le=365),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get sleep and dream patterns"""
    
    # This is a placeholder implementation
    # In a real application, you would analyze dream timing and patterns
    
    return {
        "period_days": days,
        "patterns": {
            "most_active_day": "Sunday",
            "most_active_hour": "06:00",
            "average_dreams_per_week": 3.2,
            "dream_recall_rate": 0.68
        },
        "weekly_distribution": {
            "Monday": 12,
            "Tuesday": 8,
            "Wednesday": 10,
            "Thursday": 9,
            "Friday": 11,
            "Saturday": 15,
            "Sunday": 18
        }
    }
