"""
User model
"""

from sqlalchemy import String, Boolean, DateTime, Text, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship
import uuid
from datetime import datetime, timezone
from typing import Optional, List, TYPE_CHECKING

if TYPE_CHECKING:
    from app.models.dream import Dream
    from app.models.analysis import DreamAnalysis

from app.core.database import Base


class User(Base):
    """User model"""

    __tablename__ = "users"

    # Primary key
    user_id: Mapped[str] = mapped_column(
        String(50),
        primary_key=True,
        default=lambda: f"user_{uuid.uuid4().hex[:12]}"
    )

    # Basic information
    username: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    email: Mapped[str] = mapped_column(String(255), unique=True, nullable=False)
    password_hash: Mapped[str] = mapped_column(String(255), nullable=False)

    # Profile information
    real_name: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    avatar_url: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)

    # Account status
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_verified: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)

    # Timestamps
    last_login: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    email_verified_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    deleted_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # Settings
    settings: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True, default=dict)

    # Metadata
    device_info: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True, default=dict)
    registration_ip: Mapped[Optional[str]] = mapped_column(String(45), nullable=True)

    # Statistics (denormalized for performance)
    dream_count: Mapped[int] = mapped_column(default=0, nullable=False)
    analysis_count: Mapped[int] = mapped_column(default=0, nullable=False)
    login_count: Mapped[int] = mapped_column(default=0, nullable=False)

    # Relationships
    dreams: Mapped[List["Dream"]] = relationship(
        "Dream",
        back_populates="user",
        cascade="all, delete-orphan"
    )

    analyses: Mapped[List["DreamAnalysis"]] = relationship(
        "DreamAnalysis",
        back_populates="user",
        cascade="all, delete-orphan"
    )

    def __repr__(self) -> str:
        return f"<User(user_id='{self.user_id}', username='{self.username}', email='{self.email}')>"

    @property
    def is_authenticated(self) -> bool:
        """Check if user is authenticated (active and verified)"""
        return self.is_active and self.is_verified and not self.is_deleted

    def to_dict(self, include_sensitive: bool = False) -> dict:
        """Convert user to dictionary"""
        data = {
            "user_id": self.user_id,
            "username": self.username,
            "email": self.email if include_sensitive else self.email[:3] + "***@" + self.email.split("@")[1],
            "real_name": self.real_name,
            "avatar_url": self.avatar_url,
            "is_active": self.is_active,
            "is_verified": self.is_verified,
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "email_verified_at": self.email_verified_at.isoformat() if self.email_verified_at else None,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "settings": self.settings or {},
            "statistics": {
                "dream_count": self.dream_count,
                "analysis_count": self.analysis_count,
                "login_count": self.login_count
            }
        }

        if include_sensitive:
            data.update({
                "device_info": self.device_info or {},
                "registration_ip": self.registration_ip
            })

        return data


class UserSession(Base):
    """User session model for tracking active sessions"""

    __tablename__ = "user_sessions"

    # Primary key
    session_id: Mapped[str] = mapped_column(
        String(100),
        primary_key=True,
        default=lambda: f"session_{uuid.uuid4().hex}"
    )

    # Foreign key
    user_id: Mapped[str] = mapped_column(String(50), nullable=False, index=True)

    # Session information
    refresh_token_hash: Mapped[str] = mapped_column(String(255), nullable=False)
    device_info: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True, default=dict)
    ip_address: Mapped[Optional[str]] = mapped_column(String(45), nullable=True)
    user_agent: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Status
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Timestamps
    expires_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    last_used_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=lambda: datetime.now(timezone.utc),
        nullable=False
    )

    def __repr__(self) -> str:
        return f"<UserSession(session_id='{self.session_id}', user_id='{self.user_id}')>"

    @property
    def is_expired(self) -> bool:
        """Check if session is expired"""
        return datetime.now(timezone.utc) > self.expires_at

    @property
    def is_valid(self) -> bool:
        """Check if session is valid"""
        return self.is_active and not self.is_expired


class UserLoginLog(Base):
    """User login log model"""

    __tablename__ = "user_login_logs"

    # Primary key
    log_id: Mapped[str] = mapped_column(
        String(50),
        primary_key=True,
        default=lambda: f"log_{uuid.uuid4().hex[:12]}"
    )

    # Foreign key
    user_id: Mapped[str] = mapped_column(String(50), nullable=False, index=True)

    # Login information
    ip_address: Mapped[Optional[str]] = mapped_column(String(45), nullable=True)
    user_agent: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    device_info: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True, default=dict)

    # Status
    login_successful: Mapped[bool] = mapped_column(Boolean, nullable=False)
    failure_reason: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)

    # Location (optional)
    country: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    city: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)

    def __repr__(self) -> str:
        return f"<UserLoginLog(log_id='{self.log_id}', user_id='{self.user_id}', successful={self.login_successful})>"
