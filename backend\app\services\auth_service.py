"""
Authentication service
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import Optional
from datetime import datetime, timedelta
import structlog

from app.models.user import User, UserSession, UserLoginLog
from app.core.security import security
from app.core.config import settings

logger = structlog.get_logger()


class AuthService:
    """Authentication service class"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """Authenticate user with email and password"""
        
        # Get user by email
        result = await self.db.execute(
            select(User).where(User.email == email, User.is_deleted == False)
        )
        user = result.scalar_one_or_none()
        
        if not user:
            return None
        
        # Verify password
        if not security.verify_password(password, user.password_hash):
            return None
        
        # Check if user is active
        if not user.is_active:
            return None
        
        return user
    
    async def create_session(
        self,
        user_id: str,
        refresh_token: str,
        device_info: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> UserSession:
        """Create a new user session"""
        
        # Hash the refresh token for storage
        token_hash = security.hash_password(refresh_token)
        
        # Create session
        session = UserSession(
            user_id=user_id,
            refresh_token_hash=token_hash,
            device_info={"device_id": device_info} if device_info else {},
            ip_address=ip_address,
            user_agent=user_agent,
            expires_at=datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
        )
        
        self.db.add(session)
        await self.db.commit()
        await self.db.refresh(session)
        
        return session
    
    async def get_valid_session(self, user_id: str, refresh_token: str) -> Optional[UserSession]:
        """Get valid session for user and refresh token"""
        
        # Get all active sessions for user
        result = await self.db.execute(
            select(UserSession).where(
                UserSession.user_id == user_id,
                UserSession.is_active == True
            )
        )
        sessions = result.scalars().all()
        
        # Check each session's refresh token
        for session in sessions:
            if security.verify_password(refresh_token, session.refresh_token_hash):
                if session.is_valid:
                    return session
        
        return None
    
    async def update_session_last_used(self, session_id: str) -> None:
        """Update session last used timestamp"""
        
        session = await self.db.get(UserSession, session_id)
        if session:
            session.last_used_at = datetime.utcnow()
            await self.db.commit()
    
    async def invalidate_user_sessions(self, user_id: str) -> None:
        """Invalidate all sessions for a user"""
        
        result = await self.db.execute(
            select(UserSession).where(UserSession.user_id == user_id)
        )
        sessions = result.scalars().all()
        
        for session in sessions:
            session.is_active = False
        
        await self.db.commit()
    
    async def log_login_attempt(
        self,
        email: str,
        success: bool,
        user_id: Optional[str] = None,
        failure_reason: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> None:
        """Log login attempt"""
        
        log_entry = UserLoginLog(
            user_id=user_id,
            login_successful=success,
            failure_reason=failure_reason,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        self.db.add(log_entry)
        await self.db.commit()
        
        logger.info(
            "Login attempt logged",
            email=email,
            success=success,
            user_id=user_id,
            ip_address=ip_address
        )
