"""
Dreams endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from datetime import date

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.schemas.dream import (
    DreamCreate,
    DreamUpdate,
    DreamResponse,
    DreamListResponse,
    DreamSearchRequest,
    DreamSearchResponse,
    DreamStatistics
)
from app.services.dream_service import DreamService
from app.core.exceptions import NotFoundException

router = APIRouter()


@router.post("/", response_model=DreamResponse, status_code=status.HTTP_201_CREATED)
async def create_dream(
    dream_data: DreamCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new dream"""
    dream_service = DreamService(db)
    dream = await dream_service.create_dream(current_user.user_id, dream_data)
    return dream


@router.get("/", response_model=DreamListResponse)
async def get_dreams(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user's dreams with pagination"""
    dream_service = DreamService(db)
    dreams, total = await dream_service.get_user_dreams(
        current_user.user_id, page, limit, start_date, end_date
    )
    
    total_pages = (total + limit - 1) // limit
    
    return DreamListResponse(
        total=total,
        page=page,
        limit=limit,
        total_pages=total_pages,
        dreams=dreams
    )


@router.get("/{dream_id}", response_model=DreamResponse)
async def get_dream(
    dream_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get a specific dream"""
    dream_service = DreamService(db)
    dream = await dream_service.get_dream_by_id(dream_id, current_user.user_id)
    
    if not dream:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dream not found"
        )
    
    return dream


@router.put("/{dream_id}", response_model=DreamResponse)
async def update_dream(
    dream_id: str,
    dream_data: DreamUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update a dream"""
    dream_service = DreamService(db)
    dream = await dream_service.update_dream(dream_id, current_user.user_id, dream_data)
    
    if not dream:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dream not found"
        )
    
    return dream


@router.delete("/{dream_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_dream(
    dream_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete a dream"""
    dream_service = DreamService(db)
    success = await dream_service.delete_dream(dream_id, current_user.user_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dream not found"
        )


@router.post("/search", response_model=DreamSearchResponse)
async def search_dreams(
    search_request: DreamSearchRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Search user's dreams"""
    dream_service = DreamService(db)
    dreams, total = await dream_service.search_dreams(
        current_user.user_id,
        search_request.q,
        search_request.page,
        search_request.limit
    )
    
    return DreamSearchResponse(
        total=total,
        page=search_request.page,
        limit=search_request.limit,
        keyword=search_request.q,
        dreams=[dream.__dict__ for dream in dreams]
    )


@router.get("/statistics/overview", response_model=DreamStatistics)
async def get_dream_statistics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get dream statistics for user"""
    dream_service = DreamService(db)
    stats = await dream_service.get_dream_statistics(current_user.user_id)
    return DreamStatistics(**stats)


@router.post("/{dream_id}/analyze", status_code=status.HTTP_202_ACCEPTED)
async def analyze_dream(
    dream_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Trigger dream analysis"""
    dream_service = DreamService(db)
    success = await dream_service.analyze_dream(dream_id, current_user.user_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dream not found"
        )
    
    return {"message": "Analysis started", "dream_id": dream_id}
