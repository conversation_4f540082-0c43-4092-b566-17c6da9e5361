"""
Dream service
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime, date
import structlog

from app.models.dream import Dream, DreamAnalysis, DreamKeyword, EmotionTag
from app.schemas.dream import DreamCreate, DreamUpdate
from app.core.exceptions import ValidationException, NotFoundException

logger = structlog.get_logger()


class DreamService:
    """Dream service class"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_dream(self, user_id: str, dream_data: DreamCreate) -> Dream:
        """Create a new dream"""
        
        # Create dream
        dream = Dream(
            user_id=user_id,
            dream_date=dream_data.dream_date,
            description=dream_data.description,
            emotions=dream_data.emotions,
            timezone=dream_data.timezone,
            status="pending",
            word_count=len(dream_data.description.split())
        )
        
        self.db.add(dream)
        await self.db.commit()
        await self.db.refresh(dream)
        
        logger.info("Dream created", dream_id=dream.dream_id, user_id=user_id)
        return dream
    
    async def get_dream_by_id(self, dream_id: str, user_id: str) -> Optional[Dream]:
        """Get dream by ID for specific user"""
        result = await self.db.execute(
            select(Dream).where(
                Dream.dream_id == dream_id,
                Dream.user_id == user_id,
                Dream.is_deleted == False
            )
        )
        return result.scalar_one_or_none()
    
    async def get_user_dreams(
        self,
        user_id: str,
        page: int = 1,
        limit: int = 20,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Tuple[List[Dream], int]:
        """Get user's dreams with pagination"""
        
        # Build query
        query = select(Dream).where(
            Dream.user_id == user_id,
            Dream.is_deleted == False
        )
        
        # Add date filters
        if start_date:
            query = query.where(Dream.dream_date >= start_date)
        if end_date:
            query = query.where(Dream.dream_date <= end_date)
        
        # Get total count
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # Add pagination and ordering
        query = query.order_by(desc(Dream.dream_date)).offset((page - 1) * limit).limit(limit)
        
        result = await self.db.execute(query)
        dreams = result.scalars().all()
        
        return list(dreams), total
    
    async def update_dream(self, dream_id: str, user_id: str, dream_data: DreamUpdate) -> Optional[Dream]:
        """Update dream"""
        
        dream = await self.get_dream_by_id(dream_id, user_id)
        if not dream:
            return None
        
        # Update fields
        if dream_data.dream_date is not None:
            dream.dream_date = dream_data.dream_date
        
        if dream_data.description is not None:
            dream.description = dream_data.description
            dream.word_count = len(dream_data.description.split())
        
        if dream_data.emotions is not None:
            dream.emotions = dream_data.emotions
        
        dream.updated_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(dream)
        
        logger.info("Dream updated", dream_id=dream_id, user_id=user_id)
        return dream
    
    async def delete_dream(self, dream_id: str, user_id: str) -> bool:
        """Soft delete dream"""
        
        dream = await self.get_dream_by_id(dream_id, user_id)
        if not dream:
            return False
        
        dream.is_deleted = True
        dream.deleted_at = datetime.utcnow()
        dream.updated_at = datetime.utcnow()
        
        await self.db.commit()
        logger.info("Dream deleted", dream_id=dream_id, user_id=user_id)
        return True
    
    async def search_dreams(
        self,
        user_id: str,
        query: str,
        page: int = 1,
        limit: int = 20
    ) -> Tuple[List[Dream], int]:
        """Search user's dreams"""
        
        # Simple text search in description
        search_query = select(Dream).where(
            Dream.user_id == user_id,
            Dream.is_deleted == False,
            Dream.description.ilike(f"%{query}%")
        )
        
        # Get total count
        count_query = select(func.count()).select_from(search_query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # Add pagination and ordering
        search_query = search_query.order_by(desc(Dream.dream_date)).offset((page - 1) * limit).limit(limit)
        
        result = await self.db.execute(search_query)
        dreams = result.scalars().all()
        
        return list(dreams), total
    
    async def get_dream_statistics(self, user_id: str) -> Dict[str, Any]:
        """Get dream statistics for user"""
        
        # Total dreams
        total_result = await self.db.execute(
            select(func.count()).where(
                Dream.user_id == user_id,
                Dream.is_deleted == False
            )
        )
        total_dreams = total_result.scalar()
        
        # Analyzed dreams
        analyzed_result = await self.db.execute(
            select(func.count()).where(
                Dream.user_id == user_id,
                Dream.is_deleted == False,
                Dream.status == "analyzed"
            )
        )
        analyzed_dreams = analyzed_result.scalar()
        
        # Pending dreams
        pending_dreams = total_dreams - analyzed_dreams
        
        return {
            "total_dreams": total_dreams,
            "analyzed_dreams": analyzed_dreams,
            "pending_dreams": pending_dreams,
            "avg_quality_score": None,  # Implement if needed
            "top_emotions": [],  # Implement if needed
            "top_keywords": [],  # Implement if needed
            "emotion_distribution": {}  # Implement if needed
        }
    
    async def get_emotion_tags(self) -> List[EmotionTag]:
        """Get all active emotion tags"""
        result = await self.db.execute(
            select(EmotionTag).where(EmotionTag.is_active == True).order_by(EmotionTag.sort_order)
        )
        return list(result.scalars().all())
    
    async def analyze_dream(self, dream_id: str, user_id: str) -> bool:
        """Trigger dream analysis (placeholder)"""
        
        dream = await self.get_dream_by_id(dream_id, user_id)
        if not dream:
            return False
        
        # Update status to processing
        dream.status = "processing"
        await self.db.commit()
        
        # Here you would trigger actual analysis
        # For now, just mark as analyzed
        dream.status = "analyzed"
        await self.db.commit()
        
        logger.info("Dream analysis completed", dream_id=dream_id, user_id=user_id)
        return True
